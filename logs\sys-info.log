08:33:56.095 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 7768 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
08:33:56.098 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:33:56.103 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:33:59.109 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:33:59.111 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:33:59.111 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:33:59.210 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:34:01.238 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:34:01.925 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:34:05.653 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:34:05.662 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:34:05.662 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:34:05.663 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:34:05.664 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:34:05.664 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:34:05.664 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:34:05.664 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@310cd077
08:34:05.753 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
08:34:05.754 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
08:34:05.757 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
08:34:06.730 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:34:07.125 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:34:07.136 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.469 seconds (JVM running for 12.456)
08:34:07.138 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
08:34:07.139 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
08:34:07.139 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
08:34:07.139 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
08:34:07.140 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
08:34:07.140 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
08:34:07.140 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
08:34:07.140 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
08:34:07.140 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
08:34:07.141 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
08:34:32.815 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:38:17.565 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码已失效]
08:38:19.666 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
08:38:23.148 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
08:38:48.104 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
08:39:10.373 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Success][登录成功]
08:42:08.606 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[fasuzhuguan][Logout][退出成功]
08:42:16.712 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:16:46.444 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 10072 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:16:46.447 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:16:46.448 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:16:49.080 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:16:49.083 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:16:49.083 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:16:49.171 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:16:50.928 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:16:51.605 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:16:54.549 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:16:54.559 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:16:54.559 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:16:54.560 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:16:54.561 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:16:54.562 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:16:54.562 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:16:54.562 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@68ef3a42
09:16:54.649 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:16:54.649 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:16:54.652 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:16:55.471 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:16:55.795 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:16:55.804 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.683 seconds (JVM running for 10.17)
09:16:55.806 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:16:55.806 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:16:55.806 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:16:55.806 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:16:55.807 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:16:55.807 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:16:55.808 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:16:55.808 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:16:55.808 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:16:55.809 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:18:13.247 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:18:17.351 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:27:35.064 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 3772 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:27:35.067 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:27:35.069 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:27:37.619 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:27:37.621 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:27:37.621 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:27:37.703 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:27:39.369 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:27:40.041 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:27:43.033 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:27:43.046 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:27:43.047 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:27:43.048 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:27:43.049 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:27:43.049 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:27:43.049 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:27:43.049 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d649cdb
09:27:43.129 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:27:43.130 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:27:43.132 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:27:43.947 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:27:44.269 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:27:44.278 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.551 seconds (JVM running for 10.06)
09:27:44.281 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:27:44.281 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:27:44.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:27:44.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:27:44.282 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:27:44.283 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:27:44.284 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:27:44.284 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:27:44.284 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:27:44.284 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:30:42.040 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:30:45.365 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
09:30:55.577 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Success][登录成功]
09:31:12.208 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Logout][退出成功]
09:31:15.120 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:35:35.640 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
09:35:46.367 [schedule-pool-6] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Success][登录成功]
09:39:30.610 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:39:30.608 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22280 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:39:30.612 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:39:33.202 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:39:33.204 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:39:33.204 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:39:33.283 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:39:35.157 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:39:35.892 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:39:38.875 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:39:38.885 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:39:38.886 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:39:38.886 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:39:38.887 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:39:38.887 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:39:38.887 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:39:38.887 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@48749dd8
09:39:38.980 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:39:38.981 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:39:38.984 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:39:39.873 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:39:40.177 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:39:40.186 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.914 seconds (JVM running for 10.411)
09:39:40.188 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:39:40.188 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:39:40.188 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:39:40.188 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:39:40.189 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:40:10.868 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:42:20.637 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[daihouzhuguan][Logout][退出成功]
09:42:23.433 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
10:25:13.427 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 4028 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:25:13.429 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:25:13.431 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:25:16.205 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:25:16.208 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:25:16.208 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:25:16.289 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:25:18.010 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:25:18.721 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:25:22.047 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:25:22.055 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:25:22.056 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:25:22.057 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:25:22.057 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:25:22.058 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:25:22.058 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:25:22.058 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@755abfce
10:25:22.147 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:25:22.148 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:25:22.150 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:25:23.025 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:25:23.393 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:25:23.406 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.35 seconds (JVM running for 10.875)
10:25:23.409 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:25:23.410 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:25:23.410 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:25:23.410 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:25:23.411 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:25:23.411 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:25:23.411 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:25:23.412 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:25:23.412 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:25:23.412 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:25:39.509 [http-nio-8081-exec-7] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:33:27.784 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 15996 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:33:27.788 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:33:27.789 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:33:30.350 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:33:30.357 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:33:30.357 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:33:30.447 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:33:32.160 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:33:32.860 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:33:35.789 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:33:35.800 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:33:35.801 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:33:35.803 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:33:35.804 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:33:35.804 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:33:35.804 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:33:35.804 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7b2192fa
10:33:35.897 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:33:35.897 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:33:35.901 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:33:36.740 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:33:37.075 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:33:37.085 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.646 seconds (JVM running for 10.12)
10:33:37.087 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:33:37.087 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:33:37.087 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:33:37.087 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:33:37.088 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:33:37.088 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:33:37.088 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:33:37.088 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:33:37.089 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:33:37.089 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:33:42.882 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:37:27.512 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 1768 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:37:27.515 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:37:27.516 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:37:30.321 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:37:30.324 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:37:30.324 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:37:30.406 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:37:32.145 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:37:32.845 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:37:35.816 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:37:35.825 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:37:35.826 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:37:35.826 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:37:35.827 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:37:35.827 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:37:35.827 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:37:35.828 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7b5ef332
10:37:35.931 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:37:35.932 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:37:35.935 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:37:36.782 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:37:37.120 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:37:37.128 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.987 seconds (JVM running for 10.472)
10:37:37.131 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:37:37.131 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:37:37.131 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:37:37.131 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:37:37.133 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:37:37.133 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:37:37.134 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:37:37.134 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:37:37.134 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:37:37.134 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:37:43.319 [http-nio-8081-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:39:27.294 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 13436 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:39:27.298 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:39:27.298 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:39:29.809 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:39:29.812 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:39:29.813 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:39:29.899 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:39:31.713 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:39:32.436 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:39:35.478 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:39:35.488 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:39:35.488 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:39:35.489 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:39:35.490 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:39:35.490 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:39:35.491 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:39:35.491 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@755abfce
10:39:35.576 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:39:35.577 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:39:35.579 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:39:36.429 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:39:36.806 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:39:36.818 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.865 seconds (JVM running for 10.354)
10:39:36.821 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:39:36.821 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:39:36.821 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:39:36.821 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:39:36.823 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:39:43.774 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:42:00.703 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18136 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:42:00.706 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:42:00.707 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:42:03.974 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:42:03.977 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:42:03.977 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:42:04.086 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:42:06.085 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:42:06.759 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:42:10.264 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:42:10.278 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:42:10.278 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:42:10.280 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:42:10.281 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:42:10.281 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:42:10.281 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:42:10.281 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@494506d4
10:42:10.405 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:42:10.407 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:42:10.411 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:42:11.618 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:42:11.995 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:42:12.004 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.718 seconds (JVM running for 12.358)
10:42:12.006 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:42:12.007 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:42:12.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:42:12.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:42:12.008 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:42:12.008 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:42:12.008 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:42:12.008 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:42:12.008 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:42:12.009 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:43:43.522 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:44:12.751 [http-nio-8081-exec-12] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000006
10:44:47.958 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 164 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:44:47.961 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:44:47.964 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:44:50.481 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:44:50.484 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:44:50.485 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:44:50.574 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:44:52.235 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:44:52.921 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:44:55.864 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:44:55.873 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:44:55.873 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:44:55.874 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:44:55.877 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:44:55.878 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:44:55.878 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:44:55.878 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@23c22c6f
10:44:55.969 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:44:55.970 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:44:55.972 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:44:56.834 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:44:57.198 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:44:57.208 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.591 seconds (JVM running for 10.121)
10:44:57.210 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:44:57.210 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:44:57.211 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:44:57.211 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:44:57.212 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:45:56.823 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:50:56.458 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18476 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:50:56.461 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:50:56.462 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:50:59.007 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:50:59.010 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:50:59.010 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:50:59.107 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:51:00.883 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:51:01.583 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:51:04.542 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:51:04.552 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:51:04.553 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:51:04.554 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:51:04.554 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:51:04.554 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:51:04.554 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:51:04.554 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@61e87312
10:51:04.646 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:51:04.647 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:51:04.650 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:51:05.494 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:51:05.827 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:51:05.836 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.739 seconds (JVM running for 10.221)
10:51:05.838 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:51:05.838 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:51:05.838 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:51:05.838 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:51:05.839 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:51:05.839 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:51:05.839 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:51:05.839 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:51:05.839 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:51:05.840 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:52:04.221 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:53:09.829 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12416 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:53:09.832 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:53:09.834 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:53:12.312 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:53:12.315 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:53:12.315 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:53:12.395 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:53:14.160 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:53:14.890 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:53:17.890 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:53:17.898 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:53:17.898 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:53:17.899 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:53:17.900 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:53:17.900 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:53:17.900 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:53:17.901 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@ec1798
10:53:17.985 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:53:17.985 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:53:17.988 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:53:18.794 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:53:19.114 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:53:19.126 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.646 seconds (JVM running for 10.109)
10:53:19.128 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:53:19.128 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:53:19.128 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:53:19.128 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:53:19.129 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:53:19.129 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:53:19.129 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:53:19.129 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:53:19.129 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:53:19.130 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:53:23.782 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:03:02.977 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 18820 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
11:03:02.981 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:03:02.982 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:03:05.863 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
11:03:05.866 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:03:05.866 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:03:05.949 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:03:07.773 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:03:08.478 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:03:11.446 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:03:11.453 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:03:11.453 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:03:11.454 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:03:11.455 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:03:11.455 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:03:11.455 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:03:11.456 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3980f879
11:03:11.544 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
11:03:11.544 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
11:03:11.547 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
11:03:12.361 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
11:03:12.683 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:03:12.695 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.065 seconds (JVM running for 10.567)
11:03:12.697 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
11:03:12.697 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
11:03:12.697 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
11:03:12.697 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
11:03:12.698 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
11:03:12.698 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
11:03:12.698 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
11:03:12.698 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
11:03:12.698 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
11:03:12.699 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:03:18.729 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:25:12.809 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25248 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
11:25:12.811 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:25:12.812 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:25:15.799 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
11:25:15.802 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:25:15.803 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:25:15.923 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:25:18.169 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:25:18.895 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:25:22.080 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:25:22.104 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:25:22.105 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:25:22.105 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:25:22.106 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:25:22.106 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:25:22.107 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:25:22.107 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@62318ba6
11:25:22.195 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
11:25:22.196 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
11:25:22.199 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
11:25:23.064 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
11:25:23.423 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:25:23.434 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.019 seconds (JVM running for 11.547)
11:25:23.436 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
11:25:23.436 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
11:25:23.436 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
11:25:23.436 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
11:25:23.437 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:25:33.886 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:26:02.543 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12604 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
11:26:02.546 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:26:02.547 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:26:05.488 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
11:26:05.490 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:26:05.491 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:26:05.575 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:26:07.512 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:26:08.220 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:26:11.862 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:26:11.874 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:26:11.875 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:26:11.875 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:26:11.876 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:26:11.876 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:26:11.876 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:26:11.877 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4fa01f38
11:26:11.980 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
11:26:11.981 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
11:26:11.985 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
11:26:12.904 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
11:26:13.268 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:26:13.278 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 11.1 seconds (JVM running for 11.647)
11:26:13.280 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
11:26:13.280 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
11:26:13.280 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
11:26:13.280 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
11:26:13.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
11:26:13.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
11:26:13.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
11:26:13.282 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
11:26:13.282 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
11:26:13.282 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:26:18.454 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:38:03.267 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:45:35.135 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 6600 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:45:35.138 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:45:35.139 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:45:40.296 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:45:40.299 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:45:40.299 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:45:40.383 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:45:42.258 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:45:42.985 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:45:46.559 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:45:46.570 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:45:46.570 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:45:46.573 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:45:46.574 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:45:46.574 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:45:46.574 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:45:46.574 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1c4821dd
12:45:46.661 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:45:46.662 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:45:46.664 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:45:47.513 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:45:47.834 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:45:47.843 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 13.072 seconds (JVM running for 17.892)
12:45:47.844 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:45:47.845 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:45:47.845 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:45:47.845 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:45:47.846 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:45:47.846 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:45:47.846 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:45:47.846 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:45:47.847 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:45:47.847 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:47:15.329 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
