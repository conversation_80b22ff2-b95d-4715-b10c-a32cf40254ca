package com.ruoyi.vw_litigation_case_full.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * VIEW对象 vw_litigation_case_full
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class VwLitigationCaseFull extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long 序号;

    /** 流程id */
    @Excel(name = "流程id")
    private Long 流程序号;

    /** 法诉文员 */
    @Excel(name = "法诉文员")
    private String 法诉文员;

    /** 发起法诉日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发起法诉日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 发起法诉日;

    /** 状态更新日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "状态更新日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 状态更新日;

    /** 父状态 */
    @Excel(name = "父状态")
    private String 法诉状态;

    /** 跟催员 */
    @Excel(name = "跟催员")
    private String 跟催员;

    /** 总欠款金额 */
    @Excel(name = "总欠款金额")
    private BigDecimal 总欠款金额;

    /** 已还金额 */
    @Excel(name = "已还金额")
    private BigDecimal 已还金额;

    /** 剩余金额 */
    @Excel(name = "剩余金额")
    private BigDecimal 剩余金额;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String 贷款人;

    /** 客户ID */
    @Excel(name = "客户ID")
    private String 客户ID;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String 身份证;

    /** 出单渠道 */
    @Excel(name = "出单渠道")
    private String 出单渠道;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String 地区;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String 车辆牌号;

    /** 放款银行 */
    @Excel(name = "放款银行")
    private String 放款银行;

    /** 总代偿金额 */
    @Excel(name = "总代偿金额")
    private BigDecimal 代偿总金额;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 代偿证明发出日;

    /** 法院地 */
    @Excel(name = "法院地")
    private String 法院地;

    /** 诉讼法院 */
    @Excel(name = "诉讼法院")
    private String 诉讼法院;

    /** 诉前调号 */
    @Excel(name = "诉前调号")
    private String 前调号;

    /** 诉前调号出具时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "诉前调号出具时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 前调号出具时间;

    /** 民初号 */
    @Excel(name = "民初号")
    private String 民初号;

    /** 民初号时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "民初号时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 民初号时间;

    /** 开庭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开庭时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 开庭时间;

    /** 执行号 */
    @Excel(name = "执行号")
    private String 执行号;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String 申请编号;

    /** 找车团队ID */
    @Excel(name = "找车团队ID")
    private Long 找车团队ID;

    /** 找车团队 */
    @Excel(name = "找车团队")
    private String 找车团队;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String 车辆状态;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private Integer 订单状态;

    /** 日志内容 - 来自催记表status=2的数据 */
    @Excel(name = "日志内容")
    private String 日志内容;

    /** 日志时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "日志时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date 日志时间;

    /** 日志操作人 */
    @Excel(name = "日志操作人")
    private String 日志操作人;

    /** 催记类型 */
    @Excel(name = "催记类型")
    private Integer 催记类型;

    /** 日志类型 - 前端显示用，与催记类型相同 */
    @Excel(name = "日志类型")
    private Integer 日志类型;

    /** 日志更新日 - 前端显示用，与日志时间相同 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "日志更新日", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date 日志更新日;

    /** 还款状态 */
    @Excel(name = "还款状态")
    private Integer 还款状态;

    /** 审批状态 */
    @Excel(name = "审批状态")
    private Integer 审批状态;

    /** 催回金额 */
    @Excel(name = "催回金额")
    private BigDecimal 催回金额;

    /** 约定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "约定时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 约定时间;

    /** 下次跟踪时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下次跟踪时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 下次跟踪时间;

    /** 催回总金额 */
    @Excel(name = "催回总金额")
    private BigDecimal 催回总金额;

    /** 扣款时间 - urge_status为2的最新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "扣款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 扣款时间;

    /** 业务员 */
    @Excel(name = "业务员")
    private String 业务员;

    /** 案件负责人 */
    @Excel(name = "案件负责人")
    private String 案件负责人;

    /** 法诉开始时间 - 用于区间查询 */
    private String litigationStartDate;

    /** 法诉结束时间 - 用于区间查询 */
    private String litigationEndDate;

    /** 银行代偿金额 */
    @Excel(name = "银行代偿金额")
    private BigDecimal 银行代偿金额;

    /** 银行催回金额 */
    @Excel(name = "银行催回金额")
    private BigDecimal 银行催回金额;

    /** 银行剩余未还代偿金 */
    @Excel(name = "银行剩余未还代偿金")
    private BigDecimal 银行剩余未还代偿金;

    /** 代扣金额 */
    @Excel(name = "代扣金额")
    private BigDecimal 代扣金额;

    /** 代扣催回金额 */
    @Excel(name = "代扣催回金额")
    private BigDecimal 代扣催回金额;

    /** 代扣剩余未还代偿金 */
    @Excel(name = "代扣剩余未还代偿金")
    private BigDecimal 代扣剩余未还代偿金;

    /** 违约金 */
    @Excel(name = "违约金")
    private BigDecimal 违约金;

    /** 催回违约金金额 */
    @Excel(name = "催回违约金金额")
    private BigDecimal 催回违约金金额;

    /** 剩余未还违约金金额 */
    @Excel(name = "剩余未还违约金金额")
    private BigDecimal 剩余未还违约金金额;

    /** 其他欠款 */
    @Excel(name = "其他欠款")
    private BigDecimal 其他欠款;

    /** 催回其他欠款 */
    @Excel(name = "催回其他欠款")
    private BigDecimal 催回其他欠款;

    /** 剩余未还其他欠款 */
    @Excel(name = "剩余未还其他欠款")
    private BigDecimal 剩余未还其他欠款;

    /** 案件类型 */
    @Excel(name = "案件类型")
    private Integer 案件类型;

    /** 起诉类型 */
    @Excel(name = "起诉类型")
    private String 起诉类型;

    /** 起诉内容 */
    @Excel(name = "起诉内容")
    private String 起诉内容;

    /** 起诉金额 */
    @Excel(name = "起诉金额")
    private BigDecimal 起诉金额;

    /** 案件启动日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "案件启动日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date 案件启动日;

    /** 法诉子状态 */
    @Excel(name = "法诉子状态")
    private String 法诉子状态;

    /** 执行保全号 */
    @Excel(name = "执行保全号")
    private String 执行保全号;

    public void set序号(Long 序号)
    {
        this.序号 = 序号;
    }

    public Long get序号() 
    {
        return 序号;
    }

    public void set流程序号(Long 流程序号) {
        this.流程序号 = 流程序号;
    }

    public Long get流程序号() {
        return 流程序号;
    }

    public void set法诉文员(String 法诉文员) 
    {
        this.法诉文员 = 法诉文员;
    }

    public String get法诉文员() 
    {
        return 法诉文员;
    }

    public void set发起法诉日(Date 发起法诉日) 
    {
        this.发起法诉日 = 发起法诉日;
    }

    public Date get发起法诉日() 
    {
        return 发起法诉日;
    }

    public void set状态更新日(Date 状态更新日) 
    {
        this.状态更新日 = 状态更新日;
    }

    public Date get状态更新日() 
    {
        return 状态更新日;
    }

    public void set法诉状态(String 法诉状态) 
    {
        this.法诉状态 = 法诉状态;
    }

    public String get法诉状态()
    {
        return 法诉状态;
    }

    public void set跟催员(String 跟催员)
    {
        this.跟催员 = 跟催员;
    }

    public String get跟催员()
    {
        return 跟催员;
    }

    public void set总欠款金额(BigDecimal 总欠款金额)
    {
        this.总欠款金额 = 总欠款金额;
    }

    public BigDecimal get总欠款金额()
    {
        return 总欠款金额;
    }

    public void set已还金额(BigDecimal 已还金额)
    {
        this.已还金额 = 已还金额;
    }

    public BigDecimal get已还金额()
    {
        return 已还金额;
    }

    public void set剩余金额(BigDecimal 剩余金额)
    {
        this.剩余金额 = 剩余金额;
    }

    public BigDecimal get剩余金额()
    {
        return 剩余金额;
    }

    public void set贷款人(String 贷款人)
    {
        this.贷款人 = 贷款人;
    }

    public String get贷款人()
    {
        return 贷款人;
    }

    public void set客户ID(String 客户ID)
    {
        this.客户ID = 客户ID;
    }

    public String get客户ID()
    {
        return 客户ID;
    }

    public void set身份证(String 身份证)
    {
        this.身份证 = 身份证;
    }

    public String get身份证() 
    {
        return 身份证;
    }

    public void set出单渠道(String 出单渠道) 
    {
        this.出单渠道 = 出单渠道;
    }

    public String get出单渠道() 
    {
        return 出单渠道;
    }

    public void set地区(String 地区) 
    {
        this.地区 = 地区;
    }

    public String get地区() 
    {
        return 地区;
    }

    public void set车辆牌号(String 车辆牌号) 
    {
        this.车辆牌号 = 车辆牌号;
    }

    public String get车辆牌号() 
    {
        return 车辆牌号;
    }

    public void set放款银行(String 放款银行) 
    {
        this.放款银行 = 放款银行;
    }

    public String get放款银行() 
    {
        return 放款银行;
    }

    public void set代偿总金额(BigDecimal 代偿总金额) 
    {
        this.代偿总金额 = 代偿总金额;
    }

    public BigDecimal get代偿总金额() 
    {
        return 代偿总金额;
    }

    public void set代偿证明发出日(Date 代偿证明发出日) 
    {
        this.代偿证明发出日 = 代偿证明发出日;
    }

    public Date get代偿证明发出日() 
    {
        return 代偿证明发出日;
    }

    public void set法院地(String 法院地) 
    {
        this.法院地 = 法院地;
    }

    public String get法院地() 
    {
        return 法院地;
    }

    public void set诉讼法院(String 诉讼法院) 
    {
        this.诉讼法院 = 诉讼法院;
    }

    public String get诉讼法院() 
    {
        return 诉讼法院;
    }

    public void set前调号(String 前调号) 
    {
        this.前调号 = 前调号;
    }

    public String get前调号() 
    {
        return 前调号;
    }

    public void set前调号出具时间(Date 前调号出具时间) 
    {
        this.前调号出具时间 = 前调号出具时间;
    }

    public Date get前调号出具时间() 
    {
        return 前调号出具时间;
    }

    public void set民初号(String 民初号) 
    {
        this.民初号 = 民初号;
    }

    public String get民初号() 
    {
        return 民初号;
    }

    public void set民初号时间(Date 民初号时间) 
    {
        this.民初号时间 = 民初号时间;
    }

    public Date get民初号时间() 
    {
        return 民初号时间;
    }

    public void set开庭时间(Date 开庭时间) 
    {
        this.开庭时间 = 开庭时间;
    }

    public Date get开庭时间() 
    {
        return 开庭时间;
    }

    public void set执行号(String 执行号) 
    {
        this.执行号 = 执行号;
    }

    public String get执行号()
    {
        return 执行号;
    }

    public void set申请编号(String 申请编号)
    {
        this.申请编号 = 申请编号;
    }

    public String get申请编号()
    {
        return 申请编号;
    }

    public void set找车团队ID(Long 找车团队ID)
    {
        this.找车团队ID = 找车团队ID;
    }

    public Long get找车团队ID()
    {
        return 找车团队ID;
    }

    public void set找车团队(String 找车团队)
    {
        this.找车团队 = 找车团队;
    }

    public String get找车团队()
    {
        return 找车团队;
    }

    public void set车辆状态(String 车辆状态)
    {
        this.车辆状态 = 车辆状态;
    }

    public String get车辆状态()
    {
        return 车辆状态;
    }

    public void set订单状态(Integer 订单状态)
    {
        this.订单状态 = 订单状态;
    }

    public Integer get订单状态()
    {
        return 订单状态;
    }

    public void set日志内容(String 日志内容)
    {
        this.日志内容 = 日志内容;
    }

    public String get日志内容()
    {
        return 日志内容;
    }

    public void set日志时间(Date 日志时间)
    {
        this.日志时间 = 日志时间;
    }

    public Date get日志时间()
    {
        return 日志时间;
    }

    public void set日志操作人(String 日志操作人)
    {
        this.日志操作人 = 日志操作人;
    }

    public String get日志操作人()
    {
        return 日志操作人;
    }

    public void set催记类型(Integer 催记类型)
    {
        this.催记类型 = 催记类型;
    }

    public Integer get催记类型()
    {
        return 催记类型;
    }

    public void set日志类型(Integer 日志类型)
    {
        this.日志类型 = 日志类型;
    }

    public Integer get日志类型()
    {
        return 日志类型;
    }

    public void set日志更新日(Date 日志更新日)
    {
        this.日志更新日 = 日志更新日;
    }

    public Date get日志更新日()
    {
        return 日志更新日;
    }

    public void set还款状态(Integer 还款状态)
    {
        this.还款状态 = 还款状态;
    }

    public Integer get还款状态()
    {
        return 还款状态;
    }

    public void set审批状态(Integer 审批状态)
    {
        this.审批状态 = 审批状态;
    }

    public Integer get审批状态()
    {
        return 审批状态;
    }

    public void set催回金额(BigDecimal 催回金额)
    {
        this.催回金额 = 催回金额;
    }

    public BigDecimal get催回金额()
    {
        return 催回金额;
    }

    public void set约定时间(Date 约定时间)
    {
        this.约定时间 = 约定时间;
    }

    public Date get约定时间()
    {
        return 约定时间;
    }

    public void set下次跟踪时间(Date 下次跟踪时间)
    {
        this.下次跟踪时间 = 下次跟踪时间;
    }

    public Date get下次跟踪时间()
    {
        return 下次跟踪时间;
    }

    public void set催回总金额(BigDecimal 催回总金额)
    {
        this.催回总金额 = 催回总金额;
    }

    public BigDecimal get催回总金额()
    {
        return 催回总金额;
    }

    public void set扣款时间(Date 扣款时间)
    {
        this.扣款时间 = 扣款时间;
    }

    public Date get扣款时间()
    {
        return 扣款时间;
    }

    public void set业务员(String 业务员)
    {
        this.业务员 = 业务员;
    }

    public String get业务员()
    {
        return 业务员;
    }

    public void set银行代偿金额(BigDecimal 银行代偿金额) {
        this.银行代偿金额 = 银行代偿金额;
    }

    public BigDecimal get银行代偿金额() {
        return 银行代偿金额;
    }

    public void set银行催回金额(BigDecimal 银行催回金额) {
        this.银行催回金额 = 银行催回金额;
    }

    public BigDecimal get银行催回金额() {
        return 银行催回金额;
    }

    public void set银行剩余未还代偿金(BigDecimal 银行剩余未还代偿金) {
        this.银行剩余未还代偿金 = 银行剩余未还代偿金;
    }

    public BigDecimal get银行剩余未还代偿金() {
        return 银行剩余未还代偿金;
    }

    public void set代扣金额(BigDecimal 代扣金额) {
        this.代扣金额 = 代扣金额;
    }

    public BigDecimal get代扣金额() {
        return 代扣金额;
    }

    public void set代扣催回金额(BigDecimal 代扣催回金额) {
        this.代扣催回金额 = 代扣催回金额;
    }

    public BigDecimal get代扣催回金额() {
        return 代扣催回金额;
    }

    public void set代扣剩余未还代偿金(BigDecimal 代扣剩余未还代偿金) {
        this.代扣剩余未还代偿金 = 代扣剩余未还代偿金;
    }

    public BigDecimal get代扣剩余未还代偿金() {
        return 代扣剩余未还代偿金;
    }

    public void set违约金(BigDecimal 违约金) {
        this.违约金 = 违约金;
    }

    public BigDecimal get违约金() {
        return 违约金;
    }

    public void set催回违约金金额(BigDecimal 催回违约金金额) {
        this.催回违约金金额 = 催回违约金金额;
    }

    public BigDecimal get催回违约金金额() {
        return 催回违约金金额;
    }

    public void set剩余未还违约金金额(BigDecimal 剩余未还违约金金额) {
        this.剩余未还违约金金额 = 剩余未还违约金金额;
    }

    public BigDecimal get剩余未还违约金金额() {
        return 剩余未还违约金金额;
    }

    public void set其他欠款(BigDecimal 其他欠款) {
        this.其他欠款 = 其他欠款;
    }

    public BigDecimal get其他欠款() {
        return 其他欠款;
    }

    public void set催回其他欠款(BigDecimal 催回其他欠款) {
        this.催回其他欠款 = 催回其他欠款;
    }

    public BigDecimal get催回其他欠款() {
        return 催回其他欠款;
    }

    public void set剩余未还其他欠款(BigDecimal 剩余未还其他欠款) {
        this.剩余未还其他欠款 = 剩余未还其他欠款;
    }

    public BigDecimal get剩余未还其他欠款() {
        return 剩余未还其他欠款;
    }

    public void set案件类型(Integer 案件类型) {
        this.案件类型 = 案件类型;
    }

    public Integer get案件类型() {
        return 案件类型;
    }

    public void set起诉类型(String 起诉类型) {
        this.起诉类型 = 起诉类型;
    }

    public String get起诉类型() {
        return 起诉类型;
    }

    public void set起诉内容(String 起诉内容) {
        this.起诉内容 = 起诉内容;
    }

    public String get起诉内容() {
        return 起诉内容;
    }

    public void set起诉金额(BigDecimal 起诉金额) {
        this.起诉金额 = 起诉金额;
    }

    public BigDecimal get起诉金额() {
        return 起诉金额;
    }

    public void set案件启动日(Date 案件启动日) {
        this.案件启动日 = 案件启动日;
    }

    public Date get案件启动日() {
        return 案件启动日;
    }

    public void set法诉子状态(String 法诉子状态) {
        this.法诉子状态 = 法诉子状态;
    }

    public String get法诉子状态() {
        return 法诉子状态;
    }

    public void set执行保全号(String 执行保全号) {
        this.执行保全号 = 执行保全号;
    }

    public String get执行保全号() {
        return 执行保全号;
    }

    public void setLitigationStartDate(String litigationStartDate) {
        this.litigationStartDate = litigationStartDate;
    }

    public String getLitigationStartDate() {
        return litigationStartDate;
    }

    public void setLitigationEndDate(String litigationEndDate) {
        this.litigationEndDate = litigationEndDate;
    }

    public String getLitigationEndDate() {
        return litigationEndDate;
    }

    public void set案件负责人(String 案件负责人) {
        this.案件负责人 = 案件负责人;
    }

    public String get案件负责人() {
        return 案件负责人;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("序号", get序号())
            .append("流程序号", get流程序号())
            .append("法诉文员", get法诉文员())
            .append("发起法诉日", get发起法诉日())
            .append("状态更新日", get状态更新日())
            .append("法诉状态", get法诉状态())
            .append("跟催员", get跟催员())
            .append("总欠款金额", get总欠款金额())
            .append("已还金额", get已还金额())
            .append("剩余金额", get剩余金额())
            .append("贷款人", get贷款人())
            .append("身份证", get身份证())
            .append("出单渠道", get出单渠道())
            .append("地区", get地区())
            .append("车辆牌号", get车辆牌号())
            .append("放款银行", get放款银行())
            .append("代偿总金额", get代偿总金额())
            .append("代偿证明发出日", get代偿证明发出日())
            .append("法院地", get法院地())
            .append("诉讼法院", get诉讼法院())
            .append("前调号", get前调号())
            .append("前调号出具时间", get前调号出具时间())
            .append("民初号", get民初号())
            .append("民初号时间", get民初号时间())
            .append("开庭时间", get开庭时间())
            .append("执行号", get执行号())
            .append("申请编号", get申请编号())
            .append("找车团队ID", get找车团队ID())
            .append("找车团队", get找车团队())
            .append("车辆状态", get车辆状态())
            .append("订单状态", get订单状态())
            .append("日志内容", get日志内容())
            .append("日志时间", get日志时间())
            .append("日志操作人", get日志操作人())
            .append("催记类型", get催记类型())
            .append("还款状态", get还款状态())
            .append("审批状态", get审批状态())
            .append("催回金额", get催回金额())
            .append("约定时间", get约定时间())
            .append("下次跟踪时间", get下次跟踪时间())
            .append("催回总金额", get催回总金额())
            .append("扣款时间", get扣款时间())
            .append("业务员", get业务员())
            .append("银行代偿金额", get银行代偿金额())
            .append("银行催回金额", get银行催回金额())
            .append("银行剩余未还代偿金", get银行剩余未还代偿金())
            .append("代扣金额", get代扣金额())
            .append("代扣催回金额", get代扣催回金额())
            .append("代扣剩余未还代偿金", get代扣剩余未还代偿金())
            .append("违约金", get违约金())
            .append("催回违约金金额", get催回违约金金额())
            .append("剩余未还违约金金额", get剩余未还违约金金额())
            .append("其他欠款", get其他欠款())
            .append("催回其他欠款", get催回其他欠款())
            .append("剩余未还其他欠款", get剩余未还其他欠款())
            .append("案件类型", get案件类型())
            .append("起诉类型", get起诉类型())
            .append("起诉内容", get起诉内容())
            .append("起诉金额", get起诉金额())
            .append("案件启动日", get案件启动日())
            .append("法诉子状态", get法诉子状态())
            .append("执行保全号", get执行保全号())
            .toString();
    }
}
